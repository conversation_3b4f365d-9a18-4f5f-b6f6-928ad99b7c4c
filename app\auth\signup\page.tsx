import { SignUpForm } from "@/components/auth/signup-form"
import Footer from "@/components/landing/footer"
import Header from "@/components/landing/header"
import Image from "next/image"
import { Metadata } from "next"

export const metadata: Metadata = {
  title: "Sign Up | Dental Camp",
  description: "Create your Dental Camp account",
}

export default function SignUpPage() {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      <div className="flex-1 flex">
        {/* Left Side - Form */}
        <div className="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8 py-12">
          <div className="w-full max-w-md">
            <div className="text-center mb-8">
              <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent mb-4">
                Join <PERSON> Camp
              </h1>
              <p className="text-lg text-gray-600 leading-relaxed">
                Create your account and discover premium dental solutions
              </p>
            </div>
            <SignUpForm />
          </div>
        </div>

        {/* Right Side - Image */}
        <div className="hidden lg:flex flex-1 relative bg-gradient-to-br from-blue-50 to-blue-100">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-600/10 to-blue-800/20"></div>
          <div className="relative flex items-center justify-center w-full p-12">
            <div className="text-center">
              <div className="relative mb-8">
                <Image
                  src="/images/PNSdktr.webp"
                  alt="Professional Dentist"
                  width={320}
                  height={320}
                  className="w-80 h-80 object-cover rounded-2xl shadow-2xl mx-auto"
                />
                <div className="absolute -top-4 -right-4 w-24 h-24 bg-blue-500 rounded-full opacity-20 animate-pulse"></div>
                <div className="absolute -bottom-4 -left-4 w-16 h-16 bg-blue-300 rounded-full opacity-30 animate-pulse delay-1000"></div>
              </div>
              <h2 className="text-3xl font-bold text-gray-800 mb-4">
                Professional Dental Care
              </h2>
              <p className="text-gray-600 text-lg leading-relaxed max-w-md">
                Join thousands of dental professionals who trust our premium equipment and supplies for exceptional patient care.
              </p>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  )
}
