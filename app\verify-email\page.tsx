"use client"

import { useState, Suspense } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { toast } from "sonner"
import { <PERSON>Circle, ArrowLeft, Loader2 } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { OTPVerification } from "@/components/auth/otp-verification"
import Header from "@/components/landing/header"
import Footer from "@/components/landing/footer"

function VerifyEmailContent() {
  const [isVerified, setIsVerified] = useState(false)
  const router = useRouter()
  const searchParams = useSearchParams()
  const email = searchParams.get('email')

  const handleVerificationSuccess = () => {
    setIsVerified(true)
    setTimeout(() => {
      router.push('/auth/signin')
    }, 2000)
  }

  const handleResendCode = async () => {
    if (!email) {
      toast.error("Email parameter is missing")
      return
    }

    try {
      const response = await fetch('/api/verify-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || "Failed to send verification code")
      }
    } catch (error) {
      throw error
    }
  }

  if (isVerified) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
            <CardTitle className="text-2xl text-green-600">Email Verified!</CardTitle>
            <CardDescription>
              Your email has been successfully verified. You can now sign in to your account.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <Button onClick={() => router.push('/auth/signin')} className="w-full">
              Go to Sign In
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!email) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
        <Card className="w-full max-w-md">
          <CardContent className="text-center p-8">
            <p className="text-red-600 mb-4">Email parameter is missing</p>
            <Link href="/auth/signup">
              <Button>Go to Sign Up</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-white">
      <Header />
      <div className="flex-1 flex">
        {/* Left Side - Form */}
        <div className="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8 py-12">
          <div className="w-full max-w-md">
            <div className="text-center mb-8">
              <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent mb-4">
                Verify Your Email
              </h1>
              <p className="text-lg text-gray-600 leading-relaxed">
                Enter the verification code sent to your email
              </p>
            </div>

            <OTPVerification
              email={email}
              onVerificationSuccess={handleVerificationSuccess}
              onResendCode={handleResendCode}
            />

            <div className="mt-6 text-center">
              <Link href="/auth/signin" className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800 transition-colors duration-200">
                <ArrowLeft className="w-4 h-4 mr-1" />
                Back to Sign In
              </Link>
            </div>
          </div>
        </div>

        {/* Right Side - Image */}
        <div className="hidden lg:flex flex-1 relative bg-gradient-to-br from-blue-50 to-blue-100">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-600/10 to-blue-800/20"></div>
          <div className="relative flex items-center justify-center w-full p-12">
            <div className="text-center">
              <div className="relative mb-8">
                <Image
                  src="/images/PNSdktr.webp"
                  alt="Professional Dentist"
                  width={320}
                  height={320}
                  className="w-80 h-80 object-cover rounded-2xl shadow-2xl mx-auto"
                />
                <div className="absolute -top-4 -right-4 w-24 h-24 bg-blue-500 rounded-full opacity-20 animate-pulse"></div>
                <div className="absolute -bottom-4 -left-4 w-16 h-16 bg-blue-300 rounded-full opacity-30 animate-pulse delay-1000"></div>
              </div>
              <h2 className="text-3xl font-bold text-gray-800 mb-4">
                Secure Verification
              </h2>
              <p className="text-gray-600 text-lg leading-relaxed max-w-md">
                We're verifying your email to ensure the security of your dental solutions account.
              </p>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  )
}

function LoadingFallback() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <Card className="w-full max-w-md">
        <CardContent className="flex items-center justify-center p-8">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        </CardContent>
      </Card>
    </div>
  )
}

export default function VerifyEmailPage() {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <VerifyEmailContent />
    </Suspense>
  )
}
