import { NextResponse } from "next/server"
import { auth } from "@/auth"

export async function GET() {
  try {
    const session = await auth()
    
    return NextResponse.json({
      success: true,
      session: session,
      user: session?.user || null,
      isAuthenticated: !!session?.user,
      userRole: session?.user?.role || null
    })
  } catch (error) {
    console.error('Debug session error:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to get session',
      details: error
    }, { status: 500 })
  }
}
