import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // TEMPORARILY DISABLED FOR DEBUGGING
  console.log('Middleware called for:', pathname)

  // Skip middleware for API routes, static files, and auth routes
  if (
    pathname.startsWith('/api') ||
    pathname.startsWith('/_next') ||
    pathname.startsWith('/auth') ||
    pathname.startsWith('/debug') ||
    pathname.startsWith('/test') ||
    pathname.startsWith('/setup') ||
    pathname.includes('.')
  ) {
    return NextResponse.next()
  }

  // Define protected routes
  const isAdminRoute = pathname.startsWith('/dashboard') ||
                      pathname.startsWith('/admin') ||
                      pathname.startsWith('/customers')
  const isUserRoute = pathname.startsWith('/user-space') || pathname.startsWith('/customer-space')
  const isProtectedRoute = isAdminRoute || isUserRoute

  console.log('Route check:', { pathname, isAdminRoute, isUserRoute, isProtectedRoute })

  // If not a protected route, allow access
  if (!isProtectedRoute) {
    return NextResponse.next()
  }

  // For protected routes, check for session token
  const sessionToken = request.cookies.get('next-auth.session-token') ||
                      request.cookies.get('__Secure-next-auth.session-token')

  console.log('Session token check:', { hasToken: !!sessionToken })

  if (!sessionToken) {
    // Not authenticated, redirect to signin
    console.log('No session token, redirecting to signin')
    const signInUrl = new URL('/auth/signin', request.url)
    signInUrl.searchParams.set('callbackUrl', pathname)
    return NextResponse.redirect(signInUrl)
  }

  console.log('Session token found, allowing access')
  // If we have a session token, allow access
  // Role-based checks will be handled by the page components
  return NextResponse.next()
}

export const config = {
  matcher: [
    "/((?!api|_next/static|_next/image|favicon.ico|.*\\.).*)",
  ],
}
