import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"
import { auth } from "@/auth"

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Get session
  const session = await auth()

  // Define protected routes
  const adminRoutes = ['/admin', '/dashboard']
  const userRoutes = ['/user', '/profile', '/orders']
  const authRoutes = ['/auth/signin', '/auth/signup']

  // Check if current path is an admin route
  const isAdminRoute = adminRoutes.some(route => pathname.startsWith(route))

  // Check if current path is a user route
  const isUserRoute = userRoutes.some(route => pathname.startsWith(route))

  // Check if current path is an auth route
  const isAuthRoute = authRoutes.some(route => pathname.startsWith(route))

  // If user is not authenticated and trying to access protected routes
  if (!session && (isAdminRoute || isUserRoute)) {
    const signInUrl = new URL('/auth/signin', request.url)
    signInUrl.searchParams.set('callbackUrl', pathname)
    return NextResponse.redirect(signInUrl)
  }

  // If user is authenticated and trying to access auth routes, redirect based on role
  if (session && isAuthRoute) {
    if (session.user.role === 'ADMIN') {
      return NextResponse.redirect(new URL('/dashboard', request.url))
    } else {
      return NextResponse.redirect(new URL('/user/dashboard', request.url))
    }
  }

  // If user is authenticated but trying to access admin routes without admin role
  if (session && isAdminRoute && session.user.role !== 'ADMIN') {
    return NextResponse.redirect(new URL('/user/dashboard', request.url))
  }

  // If admin is trying to access user routes, redirect to admin dashboard
  if (session && isUserRoute && session.user.role === 'ADMIN') {
    return NextResponse.redirect(new URL('/dashboard', request.url))
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    "/((?!api|_next/static|_next/image|favicon.ico|.*\\.).*)",
  ],
}
