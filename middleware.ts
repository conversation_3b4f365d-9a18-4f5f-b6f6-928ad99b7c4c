import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Define protected routes
  const adminRoutes = ['/admin', '/dashboard']
  const userRoutes = ['/user']
  const authRoutes = ['/auth/signin', '/auth/signup']

  // Check if current path is a protected route
  const isAdminRoute = adminRoutes.some(route => pathname.startsWith(route))
  const isUserRoute = userRoutes.some(route => pathname.startsWith(route))
  const isAuthRoute = authRoutes.some(route => pathname.startsWith(route))

  // Get session token from cookies (simplified check)
  const sessionToken = request.cookies.get('next-auth.session-token') ||
                      request.cookies.get('__Secure-next-auth.session-token')

  // If trying to access protected routes without session, redirect to signin
  if (!sessionToken && (isAdminRoute || isUserRoute)) {
    const signInUrl = new URL('/auth/signin', request.url)
    signInUrl.searchParams.set('callbackUrl', pathname)
    return NextResponse.redirect(signInUrl)
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    "/((?!api|_next/static|_next/image|favicon.ico|.*\\.).*)",
  ],
}
