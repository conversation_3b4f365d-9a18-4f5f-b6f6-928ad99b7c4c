import { auth } from "@/auth"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"

export default async function TestDashboardPage() {
  const session = await auth()

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>🎯 Test Dashboard (No Middleware)</CardTitle>
            <CardDescription>
              This page bypasses middleware to test authentication directly
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {!session?.user ? (
              <div className="text-center py-8">
                <h2 className="text-xl font-semibold text-red-600 mb-4">
                  ❌ Not Authenticated
                </h2>
                <p className="text-gray-600 mb-4">
                  You need to sign in to access this page.
                </p>
                <Link href="/auth/signin">
                  <Button>Go to Sign In</Button>
                </Link>
              </div>
            ) : (
              <div className="space-y-6">
                <div className="text-center py-4">
                  <h2 className="text-2xl font-semibold text-green-600 mb-2">
                    ✅ Authentication Successful!
                  </h2>
                  <p className="text-gray-600">
                    Welcome, {session.user.name || session.user.email}!
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 bg-blue-50 rounded-lg">
                    <h3 className="font-semibold mb-2">User Role</h3>
                    <Badge variant={session.user.role === "ADMIN" ? "default" : "secondary"}>
                      {session.user.role || "No Role"}
                    </Badge>
                  </div>
                  
                  <div className="p-4 bg-green-50 rounded-lg">
                    <h3 className="font-semibold mb-2">Access Level</h3>
                    <Badge variant={session.user.role === "ADMIN" ? "default" : "outline"}>
                      {session.user.role === "ADMIN" ? "Admin Access" : "User Access"}
                    </Badge>
                  </div>
                </div>
                
                <div className="p-4 bg-gray-50 rounded-lg">
                  <h3 className="font-semibold mb-2">User Information</h3>
                  <div className="space-y-2 text-sm">
                    <p><strong>ID:</strong> {session.user.id}</p>
                    <p><strong>Email:</strong> {session.user.email}</p>
                    <p><strong>Name:</strong> {session.user.name || "Not set"}</p>
                    <p><strong>Role:</strong> {session.user.role}</p>
                  </div>
                </div>
                
                <div className="flex gap-4">
                  {session.user.role === "ADMIN" ? (
                    <>
                      <Link href="/dashboard">
                        <Button className="bg-blue-600 hover:bg-blue-700">
                          Go to Admin Dashboard
                        </Button>
                      </Link>
                      <p className="text-green-600 font-medium flex items-center">
                        ✅ You have admin access!
                      </p>
                    </>
                  ) : (
                    <>
                      <Link href="/user-space">
                        <Button className="bg-green-600 hover:bg-green-700">
                          Go to User Space
                        </Button>
                      </Link>
                      <p className="text-blue-600 font-medium flex items-center">
                        ℹ️ You have user access
                      </p>
                    </>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
