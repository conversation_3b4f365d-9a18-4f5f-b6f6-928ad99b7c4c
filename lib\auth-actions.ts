"use server"

import { signIn } from "@/auth"
import { redirect } from "next/navigation"

export async function signInWithGoogle(callbackUrl?: string) {
  try {
    await signIn("google", {
      redirectTo: callbackUrl || "/dashboard"
    })
  } catch (error) {
    console.error("Google sign-in error:", error)
    // If there's an error, redirect to signin page with error
    redirect("/auth/signin?error=google_signin_failed")
  }
}
