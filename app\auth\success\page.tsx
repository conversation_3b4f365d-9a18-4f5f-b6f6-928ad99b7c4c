import { auth } from "@/auth"
import { redirect } from "next/navigation"
import { Suspense } from "react"

async function AuthSuccessContent() {
  const session = await auth()

  if (!session?.user) {
    redirect("/auth/signin")
  }

  // Add a small delay to ensure session is fully established
  await new Promise(resolve => setTimeout(resolve, 100))

  // Redirect based on role
  if (session.user.role === "ADMIN") {
    redirect("/dashboard")
  } else {
    redirect("/user-space")
  }

  return null
}

export default function AuthSuccessPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Redirecting...</p>
        </div>
      </div>
    }>
      <AuthSuccessContent />
    </Suspense>
  )
}
