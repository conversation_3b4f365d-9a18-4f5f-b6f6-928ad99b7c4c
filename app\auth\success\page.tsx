"use client"

import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { Button } from "@/components/ui/button"

export default function AuthSuccessPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [attempts, setAttempts] = useState(0)

  useEffect(() => {
    if (status === "loading") return // Still loading

    if (!session?.user) {
      router.push("/auth/signin")
      return
    }

    // Force redirect with multiple attempts
    const redirectUser = () => {
      if (session.user.role === "ADMIN") {
        console.log("Redirecting admin to dashboard, attempt:", attempts + 1)
        window.location.href = "/dashboard" // Force redirect
      } else {
        console.log("Redirecting user to user-space, attempt:", attempts + 1)
        window.location.href = "/user-space" // Force redirect
      }
    }

    // Try immediate redirect
    redirectUser()

    // Backup redirect after 1 second
    const timer = setTimeout(() => {
      redirectUser()
      setAttempts(prev => prev + 1)
    }, 1000)

    return () => clearTimeout(timer)
  }, [session, status, router, attempts])

  const handleManualRedirect = () => {
    if (session?.user?.role === "ADMIN") {
      window.location.href = "/dashboard"
    } else {
      window.location.href = "/user-space"
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center max-w-md mx-auto p-6">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Authentication Successful!</h2>
        <p className="text-gray-600 mb-4">Redirecting you to your dashboard...</p>

        {session?.user && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
            <p className="text-sm text-green-800">
              <strong>Welcome:</strong> {session.user.name || session.user.email}
            </p>
            <p className="text-sm text-green-800">
              <strong>Role:</strong> {session.user.role}
            </p>
            <p className="text-sm text-green-800">
              <strong>Redirecting to:</strong> {session.user.role === "ADMIN" ? "Admin Dashboard" : "User Space"}
            </p>
          </div>
        )}

        <div className="space-y-3">
          <Button
            onClick={handleManualRedirect}
            className="w-full bg-blue-600 hover:bg-blue-700"
          >
            Click here if not redirected automatically
          </Button>

          <div className="text-xs text-gray-500">
            Redirect attempts: {attempts}
          </div>
        </div>
      </div>
    </div>
  )
}
