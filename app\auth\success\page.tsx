"use client"

import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect } from "react"

// Custom Blue Loader Component
const BlueLoader = () => (
  <div className="flex items-center justify-center">
    <div className="relative">
      <div className="w-12 h-12 border-4 border-blue-100 rounded-lg animate-pulse"></div>
      <div className="absolute inset-0 w-12 h-12 border-4 border-blue-600 border-t-transparent rounded-lg animate-spin"></div>
    </div>
  </div>
)

export default function AuthSuccessPage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === "loading") return

    if (!session?.user) {
      router.push("/auth/signin")
      return
    }

    // Clean redirect without console logs
    const timer = setTimeout(() => {
      if (session.user.role === "ADMIN") {
        window.location.href = "/dashboard"
      } else {
        window.location.href = "/user-space"
      }
    }, 1500)

    return () => clearTimeout(timer)
  }, [session, status, router])

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-white">
      <div className="text-center">
        <BlueLoader />
        <div className="mt-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Welcome Back!
          </h2>
          <p className="text-gray-600">
            Redirecting to your dashboard...
          </p>
        </div>
      </div>
    </div>
  )
}
