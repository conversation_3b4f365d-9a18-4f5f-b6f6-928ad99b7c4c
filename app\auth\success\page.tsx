"use client"

import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect } from "react"

export default function AuthSuccessPage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === "loading") return // Still loading

    if (!session?.user) {
      router.push("/auth/signin")
      return
    }

    // Redirect based on role
    if (session.user.role === "ADMIN") {
      console.log("Redirecting admin to dashboard")
      router.push("/dashboard")
    } else {
      console.log("Redirecting user to user-space")
      router.push("/user-space")
    }
  }, [session, status, router])

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Redirecting...</p>
        {session?.user && (
          <p className="text-sm text-gray-500 mt-2">
            Welcome, {session.user.name || session.user.email}!
            Role: {session.user.role}
          </p>
        )}
      </div>
    </div>
  )
}
