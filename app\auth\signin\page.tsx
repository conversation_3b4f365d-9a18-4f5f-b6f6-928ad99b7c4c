import { Suspense } from "react"
import { SignInForm } from "@/components/auth/signin-form"
import Header from "@/components/landing/header"
import { Metadata } from "next"
import Footer from "@/components/landing/footer"

export const metadata: Metadata = {
  title: "Sign In | Dental Camp",
  description: "Sign in to your Dental Camp account",
}

export default function SignInPage() {
  return (
    <div className="min-h-screen">
      <Header />
      <div className="flex items-center justify-center sm:mt-32 mt-10 py-4 px-4 sm:px-6 lg:px-8">
        <Suspense fallback={
          <div className="flex items-center justify-center">
            <div className="loader"></div>
          </div>
        }>
          <SignInForm />
        </Suspense>
      </div>
      <Footer />
    </div>
  )
}
