import { Suspense } from "react"
import { SignInForm } from "@/components/auth/signin-form"
import { PublicHeader } from "@/components/layouts/public-header"
import { EnhancedFooter } from "@/components/layouts/enhanced-footer"
import { Metadata } from "next"

export const metadata: Metadata = {
  title: "Sign In | Dental Camp",
  description: "Sign in to your Dental Camp account",
}

export default function SignInPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50">
      <PublicHeader />
      <div className="flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <Suspense fallback={
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        }>
          <SignInForm />
        </Suspense>
      </div>
      <EnhancedFooter />
    </div>
  )
}
