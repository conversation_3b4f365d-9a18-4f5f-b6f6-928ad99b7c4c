import { Suspense } from "react"
import { SignInForm } from "@/components/auth/signin-form"
import Header from "@/components/landing/header"
import Footer from "@/components/landing/footer"
import { Metadata } from "next"

export const metadata: Metadata = {
  title: "Sign In | Dental Camp",
  description: "Sign in to your Dental Camp account",
}

export default function SignInPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50">
      <Header />
      <div className="flex items-center justify-center py-24 px-4 sm:px-6 lg:px-8">
        <Suspense fallback={
          <div className="flex items-center justify-center">
            <div className="w-8 h-8 relative">
              <div className="w-8 h-8 border-4 border-blue-100 rounded-lg animate-pulse"></div>
              <div className="absolute inset-0 w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-lg animate-spin"></div>
            </div>
          </div>
        }>
          <SignInForm />
        </Suspense>
      </div>
      <Footer />
    </div>
  )
}
