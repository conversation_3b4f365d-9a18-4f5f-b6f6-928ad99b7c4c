import { auth } from "@/auth"
import { redirect } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import Link from "next/link"

export default async function ForceDashboardPage() {
  const session = await auth()

  if (!session?.user) {
    redirect("/auth/signin")
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-2xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle>🚀 Force Dashboard Access</CardTitle>
            <CardDescription>
              Direct access to dashboard - bypassing all redirects
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h3 className="font-semibold text-green-800 mb-2">✅ Authentication Confirmed</h3>
              <div className="text-sm text-green-700 space-y-1">
                <p><strong>User:</strong> {session.user.name || session.user.email}</p>
                <p><strong>Role:</strong> {session.user.role}</p>
                <p><strong>ID:</strong> {session.user.id}</p>
              </div>
            </div>

            <div className="space-y-4">
              {session.user.role === "ADMIN" ? (
                <div className="space-y-3">
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <p className="text-blue-800 font-medium">🎯 You have ADMIN access!</p>
                  </div>
                  
                  <Link href="/dashboard">
                    <Button className="w-full bg-blue-600 hover:bg-blue-700 text-lg py-3">
                      🚀 GO TO ADMIN DASHBOARD
                    </Button>
                  </Link>
                  
                  <Button 
                    onClick={() => window.location.href = "/dashboard"}
                    variant="outline" 
                    className="w-full"
                  >
                    Force Navigate to Dashboard
                  </Button>
                </div>
              ) : (
                <div className="space-y-3">
                  <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <p className="text-orange-800 font-medium">ℹ️ You have USER access</p>
                  </div>
                  
                  <Link href="/user-space">
                    <Button className="w-full bg-green-600 hover:bg-green-700 text-lg py-3">
                      🏠 GO TO USER SPACE
                    </Button>
                  </Link>
                </div>
              )}
            </div>

            <div className="border-t pt-4">
              <h4 className="font-medium mb-2">Debug Links:</h4>
              <div className="grid grid-cols-2 gap-2">
                <Link href="/test-dashboard">
                  <Button variant="outline" size="sm" className="w-full">
                    Test Dashboard
                  </Button>
                </Link>
                <Link href="/debug-dashboard">
                  <Button variant="outline" size="sm" className="w-full">
                    Debug Info
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
