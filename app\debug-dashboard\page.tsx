import { auth } from "@/auth"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import Link from "next/link"

export default async function DebugDashboardPage() {
  const session = await auth()

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>🔍 Authentication Debug Dashboard</CardTitle>
            <CardDescription>
              Debug information for authentication system
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="font-semibold mb-2">Session Status</h3>
                <Badge variant={session ? "default" : "destructive"}>
                  {session ? "Authenticated" : "Not Authenticated"}
                </Badge>
              </div>
              
              {session?.user && (
                <>
                  <div>
                    <h3 className="font-semibold mb-2">User Role</h3>
                    <Badge variant={session.user.role === "ADMIN" ? "default" : "secondary"}>
                      {session.user.role || "No Role"}
                    </Badge>
                  </div>
                  
                  <div>
                    <h3 className="font-semibold mb-2">User ID</h3>
                    <code className="text-sm bg-gray-100 p-1 rounded">
                      {session.user.id || "No ID"}
                    </code>
                  </div>
                  
                  <div>
                    <h3 className="font-semibold mb-2">Email</h3>
                    <code className="text-sm bg-gray-100 p-1 rounded">
                      {session.user.email || "No Email"}
                    </code>
                  </div>
                  
                  <div>
                    <h3 className="font-semibold mb-2">Name</h3>
                    <code className="text-sm bg-gray-100 p-1 rounded">
                      {session.user.name || "No Name"}
                    </code>
                  </div>
                </>
              )}
            </div>
            
            {session && (
              <div className="mt-6">
                <h3 className="font-semibold mb-2">Full Session Object</h3>
                <pre className="text-xs bg-gray-100 p-4 rounded overflow-auto max-h-64">
                  {JSON.stringify(session, null, 2)}
                </pre>
              </div>
            )}
            
            <div className="flex gap-4 mt-6">
              <Link href="/auth/signin">
                <Button variant="outline">Go to Sign In</Button>
              </Link>
              
              {session?.user?.role === "ADMIN" && (
                <Link href="/dashboard">
                  <Button>Try Dashboard</Button>
                </Link>
              )}
              
              {session?.user && session.user.role !== "ADMIN" && (
                <Link href="/user-space">
                  <Button>Try User Space</Button>
                </Link>
              )}
              
              <Link href="/api/debug/session">
                <Button variant="outline">Check Session API</Button>
              </Link>
              
              <Link href="/api/debug/users">
                <Button variant="outline">Check Users API</Button>
              </Link>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>🛠️ Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Link href="/setup-admin">
                <Button variant="outline" className="w-full">
                  Setup Admin User
                </Button>
              </Link>
              
              <Link href="/auth/signin">
                <Button variant="outline" className="w-full">
                  Sign In Page
                </Button>
              </Link>
              
              <Link href="/">
                <Button variant="outline" className="w-full">
                  Home Page
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
