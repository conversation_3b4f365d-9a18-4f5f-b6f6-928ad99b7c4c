{"name": "dental-camp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 3000", "build": "prisma generate && next build", "start": "next start", "lint": "next lint", "clean": "rimraf .next", "postinstall": "prisma generate"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@auth/prisma-adapter": "^2.9.1", "@hookform/resolvers": "^5.0.1", "@prisma/client": "^6.8.2", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-table": "^8.21.3", "@types/bcryptjs": "^2.4.6", "@types/nodemailer": "^6.4.17", "@uploadthing/react": "^7.3.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.6.3", "input-otp": "^1.4.2", "lucide-react": "^0.511.0", "next": "15.2.4", "next-auth": "^5.0.0-beta.28", "nodemailer": "^6.10.1", "prop-types": "^15.8.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.4", "react-icons": "^5.5.0", "react-is": "^19.1.0", "recharts": "^2.15.3", "sonner": "^2.0.3", "tailwind-merge": "^3.1.0", "tw-animate-css": "^1.2.5", "uploadthing": "^7.7.2", "zod": "^3.25.32"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-dropzone": "^4.2.2", "eslint": "^9", "eslint-config-next": "15.2.4", "prisma": "^6.8.2", "rimraf": "^6.0.1", "tailwindcss": "^4", "tsx": "^4.19.3", "typescript": "5.8.3"}}