"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { CheckCircle, AlertCircle } from "lucide-react"

export default function SetupAdminPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)

  const setupAdmin = async () => {
    setIsLoading(true)
    setError(null)
    setResult(null)

    try {
      const response = await fetch('/api/setup-admin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const data = await response.json()

      if (data.success) {
        setResult(data)
      } else {
        setError(data.error || 'Failed to setup admin')
      }
    } catch (error) {
      setError('Network error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold text-gray-900">
            Setup Admin User
          </CardTitle>
          <CardDescription>
            Create or update the admin user for your application
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {result && (
            <Alert className="border-green-200 bg-green-50">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-800">
                <div className="space-y-2">
                  <p className="font-medium">{result.message}</p>
                  {result.credentials && (
                    <div className="text-sm">
                      <p><strong>Email:</strong> {result.credentials.email}</p>
                      <p><strong>Password:</strong> {result.credentials.password}</p>
                    </div>
                  )}
                </div>
              </AlertDescription>
            </Alert>
          )}

          <Button
            onClick={setupAdmin}
            disabled={isLoading}
            className="w-full bg-blue-600 hover:bg-blue-700"
          >
            {isLoading ? "Setting up..." : "Setup Admin User"}
          </Button>

          {result && (
            <div className="text-center">
              <a 
                href="/auth/signin" 
                className="text-blue-600 hover:text-blue-800 underline"
              >
                Go to Sign In
              </a>
            </div>
          )}

          <div className="text-center text-sm text-gray-600">
            <p>This will create an admin user with email: <EMAIL></p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
